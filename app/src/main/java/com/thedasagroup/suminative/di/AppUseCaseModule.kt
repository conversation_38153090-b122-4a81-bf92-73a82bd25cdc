package com.thedasagroup.suminative.di

import com.instacart.truetime.time.TrueTimeImpl
import com.thedasagroup.suminative.data.database.LocalOrderRepository
import com.thedasagroup.suminative.data.database.DatabaseManager
import com.thedasagroup.suminative.data.database.CategoryRepository
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.data.repo.ClockInOutRepository
import com.thedasagroup.suminative.data.repo.LoginRepository
import com.thedasagroup.suminative.data.repo.MyGuavaRepository
import com.thedasagroup.suminative.data.repo.OrdersRepository
import com.thedasagroup.suminative.data.repo.OptionRepository
import com.thedasagroup.suminative.data.repo.PrintRepository
import com.thedasagroup.suminative.data.repo.ProductRepository
import com.thedasagroup.suminative.data.repo.ReservationsRepository
import com.thedasagroup.suminative.data.repo.RewardsRepository
import com.thedasagroup.suminative.data.repo.SalesRepository
import com.thedasagroup.suminative.data.repo.StockRepository
import com.thedasagroup.suminative.data.repo.SyncRepository
import com.thedasagroup.suminative.domain.GetPOSSettingsUseCase
import com.thedasagroup.suminative.domain.cloud_print.CloudPrintUseCase
import com.thedasagroup.suminative.domain.courses_notification.SendCoursesNotificationUseCase
import com.thedasagroup.suminative.domain.myguava.MyGuavaCheckStatusUseCase
import com.thedasagroup.suminative.domain.myguava.MyGuavaCreateOrderUseCase
import com.thedasagroup.suminative.domain.myguava.MyGuavaCreateRefundOrderUseCase
import com.thedasagroup.suminative.domain.myguava.MyGuavaCreateSessionUseCase
import com.thedasagroup.suminative.domain.myguava.MyGuavaGetOrdersUseCase
import com.thedasagroup.suminative.domain.myguava.MyGuavaGetTerminalsUseCase
import com.thedasagroup.suminative.domain.myguava.MyGuavaMakePaymentUseCase
import com.thedasagroup.suminative.domain.myguava.MyGuavaMakeRefundUseCase
import com.thedasagroup.suminative.domain.orders.GetLocalOrdersUseCase
import com.thedasagroup.suminative.domain.orders.SyncOrdersUseCase
import com.thedasagroup.suminative.domain.rewards.AddPointsUseCase
import com.thedasagroup.suminative.domain.rewards.GetAllCustomersUseCase
import com.thedasagroup.suminative.domain.rewards.GetUserPointsUseCase
import com.thedasagroup.suminative.domain.rewards.GetRewardsOverviewUseCase
import com.thedasagroup.suminative.domain.sales_report.GetSalesReportUseCase
import com.thedasagroup.suminative.domain.sales_report.PrintSalesReportUseCase
import com.thedasagroup.suminative.domain.table_sync.CreateOrUpdateOrderForTableUseCase
import com.thedasagroup.suminative.ui.login.ClockInUserTimeUseCase
import com.thedasagroup.suminative.ui.login.ClockOutUserTimeUseCase
import com.thedasagroup.suminative.ui.login.GetStoreSettingsUseCase
import com.thedasagroup.suminative.ui.login.LoginUseCase
import com.thedasagroup.suminative.ui.login.StoreUserLoginUseCase
import com.thedasagroup.suminative.ui.orders.AcceptOrderWithDelayUseCase
import com.thedasagroup.suminative.ui.orders.ChangeStatusAndOrdersUseCase
import com.thedasagroup.suminative.ui.orders.ChangeStatusUseCase
import com.thedasagroup.suminative.ui.orders.CloseOpenStoreUseCase
import com.thedasagroup.suminative.ui.orders.GetOrdersUseCase
import com.thedasagroup.suminative.ui.orders.GetPendingOrdersPagedUseCase
import com.thedasagroup.suminative.ui.orders.GetScheduleOrdersPagedUseCase
import com.thedasagroup.suminative.ui.orders.GetScheduleOrdersUseCase
import com.thedasagroup.suminative.ui.products.PlaceOnlineOrderUseCase
import com.thedasagroup.suminative.ui.products.OptionDetailsUseCase
import com.thedasagroup.suminative.ui.products.OrderUseCase
import com.thedasagroup.suminative.ui.sales.TotalSalesUseCase
import com.thedasagroup.suminative.ui.stock.StockUseCase
import com.thedasagroup.suminative.ui.products.DownloadProductsUseCase
import com.thedasagroup.suminative.ui.reservations.CancelReservationUseCase
import com.thedasagroup.suminative.ui.reservations.CreateReservationUseCase
import com.thedasagroup.suminative.ui.reservations.EditReservationUseCase
import com.thedasagroup.suminative.ui.reservations.GetActiveReservationsUseCase
import com.thedasagroup.suminative.ui.reservations.GetAllReservationsUseCase
import com.thedasagroup.suminative.ui.reservations.GetReservationAreasUseCase
import com.thedasagroup.suminative.ui.reservations.GetReservationTablesUseCase
import com.thedasagroup.suminative.ui.stock.CategorySortingHelper
import com.thedasagroup.suminative.ui.stock.ChangeStockUseCase
import com.thedasagroup.suminative.domain.table_sync.DeleteOrderForTableUseCase
import com.thedasagroup.suminative.domain.table_sync.GetSyncedOrderForTableUseCase
import com.thedasagroup.suminative.domain.table_sync.SyncOrderToTableUseCase
import com.thedasagroup.suminative.domain.table_sync.ToggleTableOccupiedUseCase
import com.thedasagroup.suminative.domain.table_sync.UpdateOrderForTableUseCase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class) // Installs FooModule in the generate SingletonComponent.
internal object AppUseCaseModule {
    @Singleton
    @Provides
    fun providesLoginUseCase(loginRepository: LoginRepository, prefs: Prefs): LoginUseCase {
        return LoginUseCase(loginRepository = loginRepository, prefs = prefs)
    }

    @Singleton
    @Provides
    fun providesGetOrdersUseCase(
        ordersRepository: OrdersRepository, prefs: Prefs
    ): GetOrdersUseCase {
        return GetOrdersUseCase(repo = ordersRepository, prefs = prefs)
    }

    @Singleton
    @Provides
    fun providesScheduleOrdersUseCase(
        ordersRepository: OrdersRepository, prefs: Prefs
    ): GetScheduleOrdersUseCase {
        return GetScheduleOrdersUseCase(repo = ordersRepository, prefs = prefs)
    }

    @Singleton
    @Provides
    fun provideChangeStatusUseCase(
        prefs: Prefs, ordersRepository: OrdersRepository
    ): ChangeStatusUseCase {
        return ChangeStatusUseCase(repo = ordersRepository, prefs = prefs)
    }

    @Singleton
    @Provides
    fun providesAcceptDeliveryOrderUseCase(
        prefs: Prefs, ordersRepository: OrdersRepository,
        trueTimeImpl: TrueTimeImpl
    ): ChangeStatusAndOrdersUseCase {
        return ChangeStatusAndOrdersUseCase(
            repo = ordersRepository,
            prefs = prefs,
            trueTimeImpl = trueTimeImpl
        )
    }

    @Singleton
    @Provides
    fun providesAcceptOrderWithDelayUseCase(
        prefs: Prefs, ordersRepository: OrdersRepository, trueTimeImpl: TrueTimeImpl,
        getStoreSettingsUseCase: GetStoreSettingsUseCase
    ): AcceptOrderWithDelayUseCase {
        return AcceptOrderWithDelayUseCase(
            repo = ordersRepository, prefs = prefs, trueTimeImpl = trueTimeImpl,
            getStoreSettingsUseCase = getStoreSettingsUseCase
        )
    }

    @Singleton
    @Provides
    fun provideStoreSettingsUseCase(
        loginRepository: LoginRepository,
        prefs: Prefs
    ): GetStoreSettingsUseCase {
        return GetStoreSettingsUseCase(loginRepository = loginRepository, prefs = prefs)
    }

    @Singleton
    @Provides
    fun provideCloseOpenStoreUseCase(
        ordersRepository: OrdersRepository,
        prefs: Prefs
    ): CloseOpenStoreUseCase {
        return CloseOpenStoreUseCase(ordersRepository = ordersRepository, prefs = prefs)
    }

    @Singleton
    @Provides
    fun providePendingOrdersUseCase(
        ordersRepository: OrdersRepository,
        prefs: Prefs
    ): GetPendingOrdersPagedUseCase {
        return GetPendingOrdersPagedUseCase(repo = ordersRepository, prefs = prefs)
    }

    @Singleton
    @Provides
    fun provideScheduleOrdersPagedUseCase(
        ordersRepository: OrdersRepository,
        prefs: Prefs
    ): GetScheduleOrdersPagedUseCase {
        return GetScheduleOrdersPagedUseCase(repo = ordersRepository, prefs = prefs)
    }




    @Singleton
    @Provides
    fun providesOrderUseCase(
        stockRepository: StockRepository, prefs: Prefs, trueTimeImpl: TrueTimeImpl,
        myGuavaRepository: MyGuavaRepository,
        localOrderRepository: LocalOrderRepository
    ): OrderUseCase {
        return OrderUseCase(
            stockRepository = stockRepository, prefs = prefs, trueTimeImpl = trueTimeImpl,
            guavaRepository = myGuavaRepository,
            localOrderRepository = localOrderRepository
        )
    }

    @Singleton
    @Provides
    fun providesGuavaOrderUseCase(
        stockRepository: StockRepository, prefs: Prefs, trueTimeImpl: TrueTimeImpl,
        myGuavaRepository: MyGuavaRepository,
        rewardsRepository: RewardsRepository
    ): PlaceOnlineOrderUseCase {
        return PlaceOnlineOrderUseCase(
            stockRepository = stockRepository, prefs = prefs, trueTimeImpl = trueTimeImpl,
            guavaRepository = myGuavaRepository,
            rewardsRepository = rewardsRepository
        )
    }

    @Singleton
    @Provides
    fun providesSalesUseCase(salesRepository: SalesRepository, prefs: Prefs): TotalSalesUseCase {
        return TotalSalesUseCase(salesRepository = salesRepository, prefs = prefs)
    }

    @Singleton
    @Provides
    fun providesGetPosSettingsUsecase(
        loginRepository: LoginRepository,
        prefs: Prefs
    ): GetPOSSettingsUseCase {
        return GetPOSSettingsUseCase(loginRepository = loginRepository, prefs = prefs)
    }

    @Singleton
    @Provides
    fun providesSalesReportUsecase(
        salesRepository: SalesRepository,
        prefs: Prefs
    ): GetSalesReportUseCase {
        return GetSalesReportUseCase(salesRepository = salesRepository, prefs = prefs)
    }

    @Singleton
    @Provides
    fun providesMyGuavaCreateOrderUseCase(
        myGuavaRepository: MyGuavaRepository,
        trueTimeImpl: TrueTimeImpl,
        prefs: Prefs
    ): MyGuavaCreateOrderUseCase {
        return MyGuavaCreateOrderUseCase(guavaRepository = myGuavaRepository,
            trueTimeImpl = trueTimeImpl,
            prefs = prefs)
    }

    @Singleton
    @Provides
    fun providesMyGuavaGetTerminalsUseCase(
        myGuavaRepository: MyGuavaRepository
    ): MyGuavaGetTerminalsUseCase {
        return MyGuavaGetTerminalsUseCase(guavaRepository = myGuavaRepository)
    }

    @Singleton
    @Provides
    fun providesMyGuavaCreateSessionUseCase(
        myGuavaRepository: MyGuavaRepository
    ): MyGuavaCreateSessionUseCase {
        return MyGuavaCreateSessionUseCase(guavaRepository = myGuavaRepository)
    }

    @Singleton
    @Provides
    fun providesMyGuavaMakePaymentUseCase(
        orderUseCase: MyGuavaCreateOrderUseCase,
        terminalsUseCase: MyGuavaGetTerminalsUseCase,
        createSessionUseCase: MyGuavaCreateSessionUseCase
    ): MyGuavaMakePaymentUseCase {
        return MyGuavaMakePaymentUseCase(
            orderUseCase,
            terminalsUseCase,
            createSessionUseCase
        )
    }

    @Singleton
    @Provides
    fun providesMyGuavaCheckStatusUseCase(
        myGuavaRepository: MyGuavaRepository
    ): MyGuavaCheckStatusUseCase {
        return MyGuavaCheckStatusUseCase(guavaRepository = myGuavaRepository)
    }

    @Singleton
    @Provides
    fun providesMyGuavaGetOrderUseCase(
        myGuavaRepository: MyGuavaRepository,
        trueTimeImpl: TrueTimeImpl
    ): MyGuavaGetOrdersUseCase {
        return MyGuavaGetOrdersUseCase(guavaRepository = myGuavaRepository, trueTimeImpl = trueTimeImpl)
    }
    @Singleton
    @Provides
    fun providesMyGuavaRefundOrderUseCase(
        myGuavaRepository: MyGuavaRepository,
        trueTimeImpl: TrueTimeImpl
    ): MyGuavaCreateRefundOrderUseCase {
        return MyGuavaCreateRefundOrderUseCase(guavaRepository = myGuavaRepository)
    }

    @Singleton
    @Provides
    fun providesMyGuavaMakeRefundUseCase(
        createOrderUseCase : MyGuavaCreateRefundOrderUseCase,
        getTerminalsUseCase: MyGuavaGetTerminalsUseCase,
        createSessionUseCase: MyGuavaCreateSessionUseCase
    ): MyGuavaMakeRefundUseCase {
        return MyGuavaMakeRefundUseCase(
            createOrderUseCase = createOrderUseCase,
            getTerminalsUseCase = getTerminalsUseCase,
            createSessionUseCase = createSessionUseCase
        )
    }

    @Singleton
    @Provides
    fun providesLocalOrdersUseCase(
        ordersRepository: LocalOrderRepository,
        prefs: Prefs
    ): GetLocalOrdersUseCase {
        return GetLocalOrdersUseCase(
            orderRepository = ordersRepository
        )
    }

    @Singleton
    @Provides
    fun providesSyncOrdersUseCase(
        localOrderRepository: LocalOrderRepository,
        stockRepository: StockRepository,
        prefs: Prefs
    ): SyncOrdersUseCase {
        return SyncOrdersUseCase(
            localOrderRepository = localOrderRepository,
            stockRepository = stockRepository,
            prefs = prefs
        )
    }

    @Provides
    fun provideDownloadProductsUseCase(
        stockRepository: StockRepository,
        productRepository: ProductRepository,
        optionRepository: OptionRepository,
        categoryRepository: CategoryRepository,
        prefs: Prefs
    ): DownloadProductsUseCase = DownloadProductsUseCase(stockRepository, productRepository, optionRepository, categoryRepository, prefs)

    @Provides
    @Singleton
    fun provideProductRepository(databaseManager: DatabaseManager): ProductRepository = ProductRepository(databaseManager)

    @Provides
    @Singleton
    fun provideOptionRepository(databaseManager: DatabaseManager): OptionRepository = OptionRepository(databaseManager)

    @Singleton
    @Provides
    fun providesStoreUserLoginUseCase(clockInOutRepository: ClockInOutRepository): StoreUserLoginUseCase {
        return StoreUserLoginUseCase(clockInOutRepository = clockInOutRepository)
    }

    @Singleton
    @Provides
    fun providesClockInUserTimeUseCase(clockInOutRepository: ClockInOutRepository): ClockInUserTimeUseCase {
        return ClockInUserTimeUseCase(clockInOutRepository = clockInOutRepository)
    }

    @Singleton
    @Provides
    fun providesClockOutUserTimeUseCase(clockInOutRepository: ClockInOutRepository): ClockOutUserTimeUseCase {
        return ClockOutUserTimeUseCase(clockInOutRepository = clockInOutRepository)
    }

    @Singleton
    @Provides
    fun providesGetActiveReservationsUseCase(
        reservationsRepository: ReservationsRepository,
        prefs: Prefs,
        trueTimeImpl: TrueTimeImpl
    ): GetActiveReservationsUseCase {
        return GetActiveReservationsUseCase(reservationsRepository = reservationsRepository, prefs = prefs,
            trueTimeImpl = trueTimeImpl)
    }

    @Singleton
    @Provides
    fun providesGetAllReservationsUseCase(
        reservationsRepository: ReservationsRepository,
        prefs: Prefs,
        trueTimeImpl: TrueTimeImpl
    ): GetAllReservationsUseCase {
        return GetAllReservationsUseCase(reservationsRepository = reservationsRepository, prefs = prefs,
            trueTimeImpl = trueTimeImpl)
    }

    @Singleton
    @Provides
    fun providesGetReservationAreasUseCase(
        reservationsRepository: ReservationsRepository,
        prefs: Prefs
    ): GetReservationAreasUseCase {
        return GetReservationAreasUseCase(reservationsRepository = reservationsRepository, prefs = prefs)
    }

    @Singleton
    @Provides
    fun providesGetReservationTablesUseCase(
        reservationsRepository: ReservationsRepository
    ): GetReservationTablesUseCase {
        return GetReservationTablesUseCase(reservationsRepository = reservationsRepository)
    }

    @Singleton
    @Provides
    fun providesCreateReservationUseCase(reservationsRepository: ReservationsRepository): CreateReservationUseCase {
        return CreateReservationUseCase(reservationsRepository = reservationsRepository)
    }

    @Singleton
    @Provides
    fun providesEditReservationUseCase(reservationsRepository: ReservationsRepository): EditReservationUseCase {
        return EditReservationUseCase(reservationsRepository = reservationsRepository)
    }

    @Singleton
    @Provides
    fun providesCancelReservationUseCase(reservationsRepository: ReservationsRepository): CancelReservationUseCase {
        return CancelReservationUseCase(reservationsRepository = reservationsRepository)
    }

    @Singleton
    @Provides
    fun providesCloudPrintUseCase(stockRepository: StockRepository): CloudPrintUseCase {
        return CloudPrintUseCase(stockRepository = stockRepository)
    }

    @Singleton
    @Provides
    fun providesGetAllCustomersUseCase(rewardsRepository: RewardsRepository): GetAllCustomersUseCase {
        return GetAllCustomersUseCase(rewardsRepository = rewardsRepository)
    }

    @Singleton
    @Provides
    fun providesAddPointsUseCase(rewardsRepository: RewardsRepository): AddPointsUseCase {
        return AddPointsUseCase(rewardsRepository = rewardsRepository)
    }

    @Singleton
    @Provides
    fun providesGetUserPointsUseCase(rewardsRepository: RewardsRepository): GetUserPointsUseCase {
        return GetUserPointsUseCase(rewardsRepository = rewardsRepository)
    }

    @Singleton
    @Provides
    fun providesGetRewardsOverviewUseCase(rewardsRepository: RewardsRepository): GetRewardsOverviewUseCase {
        return GetRewardsOverviewUseCase(rewardsRepository = rewardsRepository)
    }

    @Provides
    @Singleton
    fun providesSendCoursesNotificationUseCase(
        printRepository: PrintRepository,
    ) : SendCoursesNotificationUseCase{
        return SendCoursesNotificationUseCase(
            printRepository = printRepository
        )
    }

    @Provides
    @Singleton
    fun providesSyncOrderToTableUseCase(
        syncRepository: SyncRepository
    ): SyncOrderToTableUseCase {
        return SyncOrderToTableUseCase(syncRepository = syncRepository)
    }

    @Provides
    @Singleton
    fun providesGetSyncedOrderForTableUseCase(
        syncRepository: SyncRepository
    ): GetSyncedOrderForTableUseCase {
        return GetSyncedOrderForTableUseCase(syncRepository = syncRepository)
    }

    @Provides
    @Singleton
    fun providesToggleTableOccupiedUseCase(
        syncRepository: SyncRepository
    ): ToggleTableOccupiedUseCase {
        return ToggleTableOccupiedUseCase(syncRepository = syncRepository)
    }

    @Provides
    @Singleton
    fun providesUpdateOrderForTableUseCase(
        syncRepository: SyncRepository
    ): UpdateOrderForTableUseCase {
        return UpdateOrderForTableUseCase(syncRepository = syncRepository)
    }

    @Provides
    @Singleton
    fun providesDeleteOrderForTableUseCase(
        syncRepository: SyncRepository
    ): DeleteOrderForTableUseCase {
        return DeleteOrderForTableUseCase(syncRepository = syncRepository)
    }


    @Provides
    @Singleton
    fun providesCreateOrUpdateOrderForTableUseCase(
        syncRepository: SyncRepository,
        prefs: Prefs
    ): CreateOrUpdateOrderForTableUseCase {
        return CreateOrUpdateOrderForTableUseCase(syncRepository = syncRepository, prefs = prefs)
    }

    @Provides
    @Singleton
    fun providesPrintSalesReportUseCase(
        salesRepository: SalesRepository,
        prefs: Prefs
    ): PrintSalesReportUseCase {
        return PrintSalesReportUseCase(salesRepository = salesRepository, prefs = prefs)
    }

}

