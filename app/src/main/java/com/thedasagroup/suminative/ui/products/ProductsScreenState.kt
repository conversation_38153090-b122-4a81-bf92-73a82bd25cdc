package com.thedasagroup.suminative.ui.products

import com.airbnb.mvrx.Async
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Uninitialized
import com.thedasagroup.suminative.data.model.request.order.Cart
import com.thedasagroup.suminative.data.model.request.order.Order
import com.thedasagroup.suminative.data.model.request.pagination.OrderItem2
import com.thedasagroup.suminative.data.model.request.sales.SalesRequest
import com.thedasagroup.suminative.data.model.response.courses_notification.CoursesNotificationResponse
import com.thedasagroup.suminative.data.model.response.options_details.OptionDetails
import com.thedasagroup.suminative.data.model.response.order.OrderResponse2
import com.thedasagroup.suminative.data.model.response.sales.SalesReportResponse
import com.thedasagroup.suminative.data.model.response.sales.SalesResponse
import com.thedasagroup.suminative.data.model.response.stock.StockItem
import com.thedasagroup.suminative.data.model.response.stock.StockItemsResponse
import com.thedasagroup.suminative.ui.products.cart.CartTab
import com.thedasagroup.suminative.ui.products.cart.CourseStatus
import com.thedasagroup.suminative.ui.reservations.AreaTableSelectionHelper
import com.thedasagroup.suminative.work.SyncStatus

data class ProductsScreenState(
    val stockItemsResponse: Async<StockItemsResponse> = Uninitialized,
    val stockResponse: Async<StockItemsResponse> = Uninitialized,
    val stock: Int = 0,
    val showUpdateStockDialog: StockItem? = null,
    val isBottomSheetVisible: Cart? = null,
    val order: Order = Order(), // Deprecated - kept for backward compatibility
    val tableOrders: Map<Int, Order> = emptyMap(), // Table-specific orders (tableId -> Order)
    val orderResponse: Async<OrderResponse2> = Uninitialized,
    val salesResponse: Async<SalesResponse> = Uninitialized,
    val optionDetailsResponse: Async<OptionDetails> = Uninitialized,
    val productTotal: Double = 0.0,
    val isShowPrintingPreview: OrderItem2? = null,
    val shouldPrintInstant: Boolean = false,
    val showCart: Boolean = false,
    val salesReportResponse: Async<SalesReportResponse> = Uninitialized,
    val showSalesReportDialog: Boolean = false,
    val salesRequest: SalesRequest? = null,
    val refreshing: Boolean = false,
    val syncStatus: SyncStatus = SyncStatus.Idle,
    val selectedTables: List<AreaTableSelectionHelper.AreaTableSelection> = emptyList(),
    val selectedTableIndex: Int = 0,
    // Customer management state
    val selectedCustomer: com.thedasagroup.suminative.data.model.response.rewards.RewardsCustomer? = null, // Global selected customer (for walk-in customers)
    val tableCustomers: Map<Int, com.thedasagroup.suminative.data.model.response.rewards.RewardsCustomer> = emptyMap(), // Table-specific customers (tableId -> RewardsCustomer)
    // Course management state
    val cartItemsWithCourses: Map<Int, List<CartItemWithCourse>> = emptyMap(), // Table-specific course assignments (tableId -> List<CartItemWithCourse>)
    val selectedCourseFilter: Map<Int, CourseFilter> = emptyMap(), // Table-specific course filter (tableId -> CourseFilter)
    val globalCartItemsWithCourses: List<CartItemWithCourse> = emptyList(), // Global course assignments when no tables
    val globalSelectedCourseFilter: CourseFilter = CourseFilter.ALL, // Global course filter when no tables
    val availableCourses: List<MealCourse> = listOf(), // Global courses (for walk-in customers)
    val tableAvailableCourses: Map<Int, List<MealCourse>> = emptyMap(), // Table-specific available courses (tableId -> List<MealCourse>)
    val selectedCourseForNewItems: String = "", // Global selected course for new items (for walk-in customers)
    val tableSelectedCourseForNewItems: Map<Int, String> = emptyMap(), // Table-specific selected course for new items (tableId -> courseId)
    val courseStatuses: Map<String, CourseStatus> = emptyMap(), // Global course statuses (for walk-in customers)
    val tableCourseStatuses: Map<Int, Map<String, CourseStatus>> = emptyMap(), // Table-specific course statuses (tableId -> courseId -> CourseStatus)
    val currentActiveCourse: String? = null, // Global active course (for walk-in customers)
    val tableActiveCourses: Map<Int, String> = emptyMap(), // Table-specific active courses (tableId -> courseId)
    // Course status queues for ordered progression
    val courseStatusQueue: CourseStatusQueue = CourseStatusQueue(), // Global course status queue (for walk-in customers)
    val tableCourseStatusQueues: Map<Int, CourseStatusQueue> = emptyMap(), // Table-specific course status queues (tableId -> CourseStatusQueue)
    // Service charge state
    val serviceChargeApplied: Boolean = false, // Global service charge applied (for walk-in customers)
    val tableServiceChargeApplied: Map<Int, Boolean> = emptyMap(), // Table-specific service charge applied (tableId -> Boolean)
    val tableServiceChargeManuallyRemoved: Map<Int, Boolean> = emptyMap(), // Track if user manually removed service charge for specific tables (tableId -> Boolean),
    val selectedTableBasedCartTab : Map<Int, CartTab> = mutableMapOf(),
    val cartTab: CartTab = CartTab.ORDER,
    val appliedPrefill : Cart? = null,
    val printSalesReportResponse: Async<SalesReportResponse> = Uninitialized,
    val printResponse : Async<CoursesNotificationResponse> = Uninitialized,
    val showTableUnselectedAlert: Boolean = false,
) : MavericksState {

    fun getSelectedCartTab() : CartTab{
        val currentTableId = getCurrentTableId()
        return if( currentTableId != null){
            selectedTableBasedCartTab[currentTableId] ?: CartTab.ORDER
        }
        else{
            cartTab
        }
    }

    /**
     * Get the current table's order based on selectedTableIndex
     */
    fun getCurrentTableOrder(): Order {
        return if (selectedTables.isNotEmpty() && selectedTableIndex < selectedTables.size && selectedTableIndex >= 0) {
            val currentTable = selectedTables[selectedTableIndex]
            tableOrders[currentTable.tableId] ?: Order()
        } else {
            // Fallback to global order if no tables selected
            order
        }
    }

    /**
     * Get the current table ID, or null if no table is selected
     */
    fun getCurrentTableId(): Int? {
        return if (selectedTables.isNotEmpty() && selectedTableIndex < selectedTables.size && selectedTableIndex >= 0) {
            selectedTables[selectedTableIndex].tableId
        } else {
            null
        }
    }

    /**
     * Get the customer for the current table or global customer
     */
    fun getCurrentCustomer(): com.thedasagroup.suminative.data.model.response.rewards.RewardsCustomer? {
        val currentTableId = getCurrentTableId()
        return if (currentTableId != null) {
            tableCustomers[currentTableId]
        } else {
            selectedCustomer
        }
    }

    /**
     * Get cart items with courses for the current table
     */
    fun getCurrentTableCartItemsWithCourses(): List<CartItemWithCourse> {
        val currentTableId = getCurrentTableId()
        return if (currentTableId != null) {
            cartItemsWithCourses[currentTableId]?.filter {
                it.courseId.isNotEmpty()
            } ?: emptyList() ?: emptyList()
        } else {
            // Use global course assignments when no tables selected
            if (globalCartItemsWithCourses.isNotEmpty()) {
                globalCartItemsWithCourses?.filter {
                    it.courseId.isNotEmpty()
                } ?: emptyList()
            }
            else emptyList()
        }
    }

    fun getCurrentTableCartItemsWithoutCourses() : List<CartItemWithCourse>{
        val currentTableId = getCurrentTableId()
        return if (currentTableId != null) {
            cartItemsWithCourses[currentTableId]?.filter {
                !it.courseId.isNotEmpty()
            } ?: emptyList()
        } else {
            // Use global course assignments when no tables selected
            if (globalCartItemsWithCourses.isNotEmpty()) {
                globalCartItemsWithCourses?.filter {
                    !it.courseId.isNotEmpty()
                } ?: emptyList()
            } else {
                emptyList()
            }
        }
    }

    /**
     * Get the current course filter for the current table
     */
    fun getCurrentTableCourseFilter(): CourseFilter {
        val currentTableId = getCurrentTableId()
        return if (currentTableId != null) {
            selectedCourseFilter[currentTableId] ?: CourseFilter.ALL
        } else {
            // Use global course filter when no tables selected
            globalSelectedCourseFilter
        }
    }

    /**
     * Get filtered cart items based on current table's course filter
     */
    fun getFilteredCartItems(): List<CartItemWithCourse> {
        val cartItems = getCurrentTableCartItemsWithCourses()
        val filter = getCurrentTableCourseFilter()

        return when (filter) {
            CourseFilter.ALL -> cartItems
            CourseFilter.STARTERS -> cartItems.filter { it.courseId == "course_starters" }
            CourseFilter.MAINS -> cartItems.filter { it.courseId == "course_mains" }
            CourseFilter.DESSERTS -> cartItems.filter { it.courseId == "course_desserts" }
        }
    }

    fun isCourseSelected(courseId: String): Boolean {
        val currentTableId = getCurrentTableId()
        return if (currentTableId != null) {
            tableSelectedCourseForNewItems[currentTableId] == courseId
        } else {
            selectedCourseForNewItems == courseId
        }
    }

    /**
     * Get course counts for the current table
     */
    fun getCourseCounts(): Map<CourseFilter, Int> {
        val cartItems = getCurrentTableCartItemsWithCourses()
        return mapOf(
            CourseFilter.STARTERS to cartItems.count { it.courseId == "course_starters" },
            CourseFilter.MAINS to cartItems.count { it.courseId == "course_mains" },
            CourseFilter.DESSERTS to cartItems.count { it.courseId == "course_desserts" }
        )
    }

    /**
     * Get available courses for the current table or global courses
     */
    fun getCurrentTableAvailableCourses(): List<MealCourse> {
        val currentTableId = getCurrentTableId()
        return if (currentTableId != null) {
            tableAvailableCourses[currentTableId] ?: emptyList()
        } else {
            availableCourses
        }
    }

    /**
     * Get selected course for new items for the current table or global
     */
    fun getCurrentTableSelectedCourseForNewItems(): String {
        val currentTableId = getCurrentTableId()
        return if (currentTableId != null) {
            tableSelectedCourseForNewItems[currentTableId] ?: ""
        } else {
            selectedCourseForNewItems
        }
    }

    /**
     * Get service charge amount for the current order
     */
    fun getServiceChargeAmount(serviceChargePercentage: Double): Double {
        if (isServiceChargeApplied()) {
            val currentOrder = getCurrentTableOrder()
            val netPayable = currentOrder.net()
            return netPayable * (serviceChargePercentage / 100.0)
        } else return 0.0
    }

    /**
     * Check if service charge is applied for the current table or global
     */
    fun isServiceChargeApplied(): Boolean {
        val currentTableId = getCurrentTableId()
        return if (currentTableId != null) {
            tableServiceChargeApplied[currentTableId] ?: false
        } else {
            serviceChargeApplied
        }
    }

    /**
     * Get the current course status queue for the given context
     */
    fun getStatusQueue(): CourseStatusQueue {
        val currentTableId = getCurrentTableId()
        return if (currentTableId != null) {
            tableCourseStatusQueues[currentTableId] ?: CourseStatusQueue()
        } else {
            courseStatusQueue
        }
    }


}