package com.thedasagroup.suminative.ui.products

import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.tween
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.BottomNavigation
import androidx.compose.material.BottomNavigationItem
import androidx.compose.material.FloatingActionButton
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.KeyboardArrowLeft
import androidx.compose.material.icons.filled.KeyboardArrowRight
import androidx.compose.material.icons.outlined.Add
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.ScrollableTabRow
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.clipToBounds
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Color.Companion.Black
import androidx.compose.ui.graphics.Color.Companion.White
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.lifecycleScope
import androidx.navigation.NavController
import androidx.navigation.NavGraph.Companion.findStartDestination
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import coil.compose.AsyncImage
import coil.request.CachePolicy
import coil.request.ImageRequest
import com.afollestad.materialdialogs.MaterialDialog
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.compose.collectAsState
import com.thedasagroup.suminative.AssignTableDialog
import com.thedasagroup.suminative.CATEGORY_GREEN_COLOR
import com.thedasagroup.suminative.RegularOrders
import com.thedasagroup.suminative.Reservations
import com.thedasagroup.suminative.StockManagement
import com.thedasagroup.suminative.StoreItems
import com.thedasagroup.suminative.Tables
import com.thedasagroup.suminative.TopLevelRoute
import com.thedasagroup.suminative.data.api.BASE_DOMAIN
import com.thedasagroup.suminative.data.model.request.order.Order
import com.thedasagroup.suminative.data.model.response.stock.StockItem
import com.thedasagroup.suminative.printOrderBitmap
import com.thedasagroup.suminative.topLevelRoutes
import com.thedasagroup.suminative.topLevelRoutesReservations
import com.thedasagroup.suminative.ui.MainActivity
import com.thedasagroup.suminative.ui.orders.OrderScreenTopFunction
import com.thedasagroup.suminative.ui.orders.OrderScreenViewModel
import com.thedasagroup.suminative.ui.orders.OrderState
import com.thedasagroup.suminative.ui.reservations.AreaTableSelectionHelper
import com.thedasagroup.suminative.ui.reservations.AreaTableSelectionScreen
import com.thedasagroup.suminative.ui.reservations.ReservationsScreen
import com.thedasagroup.suminative.ui.reservations.ReservationsViewModel
import com.thedasagroup.suminative.ui.stock.StockScreen
import com.thedasagroup.suminative.ui.stock.StockScreenViewModel
import com.thedasagroup.suminative.ui.theme.SumiNativeTheme
import com.thedasagroup.suminative.ui.theme.fontPoppins
import com.thedasagroup.suminative.ui.utils.transformDecimal
import ir.kaaveh.sdpcompose.sdp
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.UUID


@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SplitScreen(
    viewModel: ProductsScreenViewModel,
    order: Order,
    left : @Composable () -> Unit,
    right : @Composable () -> Unit,
) {
    val listCarts = order.carts ?: emptyList()
    val showCart by viewModel.collectAsState(ProductsScreenState::showCart)

    LaunchedEffect(key1 = "callStockApi") {
        withContext(Dispatchers.IO){
            viewModel.getStockItems()
        }
    }

    Row(modifier = Modifier.fillMaxSize()) {
        Column(modifier = Modifier.weight(0.7f)) {
            left()
        }
        if(listCarts.isNotEmpty() && showCart) {
            Column(modifier = Modifier.weight(0.3f)) {
                right()
            }
        }
    }
}

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun ProductsScreen(
    viewModel: ProductsScreenViewModel,
    onBackClick: () -> Unit,
    onOpenProductDetails: (StockItem) -> Unit,
    openCart: () -> Unit,
    stockScreenViewModel : StockScreenViewModel,
    orderScreenViewModel: OrderScreenViewModel,
    mainActivity: MainActivity,
    addDirectlyToCart: (StockItem) -> Unit,
    onTableSelected: () -> Unit,
    reservationsViewModel: ReservationsViewModel,
    onRemoveCustomer : () -> Unit,
    onAddNewTableClick : () -> Unit,
    onTableRemove: (Int) -> Unit
) {
    val state by viewModel.collectAsState()
    val coroutineScope = rememberCoroutineScope()

    // State for cart clear confirmation dialog
    var showCartClearDialog by remember { mutableStateOf(false) }
    var pendingTableSelection by remember { mutableStateOf<AreaTableSelectionHelper.AreaTableSelection?>(null) }
    var showAssignTableDialog by remember { mutableStateOf(false) }
    val selectedCustomer = state.selectedCustomer
    val reservationState by reservationsViewModel.collectAsState()
    val showTableUnselectedAlert by viewModel.collectAsState(ProductsScreenState::showTableUnselectedAlert)

    // Confirmation dialog for cart clearing when selecting table
    if (showCartClearDialog && pendingTableSelection != null) {
        AssignTableDialog(
            isTableOccupied = pendingTableSelection?.isOccupied ?: false,
            onDismiss = {
                showCartClearDialog = false
                pendingTableSelection = null
            },
            onAssignTableClick = {
                showCartClearDialog = false
                pendingTableSelection?.let { selection ->
                    // Assign global cart items to the selected table
                    viewModel.assignGlobalCartToTable(selection, state = state)
                    // Call handleUnoccupiedTableSelection to sync the newly selected table
                    selection.table?.let { table ->
                        if (!table.occupied) {
                            coroutineScope.launch {
                                viewModel.handleUnoccupiedTableSelection(selection, table, state = state)
                            }
                        }
                    }
                }
                orderScreenViewModel.updateCurrentRoute("0")
            },
            onClearCartClick = {
                pendingTableSelection?.let { selection ->
                    // Clear global cart and continue with table selection based on occupied status
                    viewModel.clearGlobalCart()
                    pendingTableSelection?.let { pendingSelection ->
                        pendingSelection.table?.let {
                            viewModel.handleTableSelectionWithOccupiedStatus(selection, it, state = state, shouldCallSaveTable = true)
                        }
                    }
                }
                showCartClearDialog = false
                pendingTableSelection = null
                orderScreenViewModel.updateCurrentRoute("0")
            }
        )
    }

    // Helper function to handle table selection with cart clearing logic
    val handleTableSelection = { selection: AreaTableSelectionHelper.AreaTableSelection ->
        if (state.selectedTables.isEmpty() && viewModel.hasGlobalCartItems(state = state)) {
            // Show confirmation dialog if no tables selected and global cart has items
            pendingTableSelection = selection
            showCartClearDialog = true
        } else {
            // Directly add table if no global cart items or tables already selected
            viewModel.addSelectedTable(selection, state = state)
            orderScreenViewModel.updateCurrentRoute("0")
        }

        if(state.selectedTables.isEmpty() && selectedCustomer != null){
            //TODO update customer
        }
    }


    LaunchedEffect(key1 = "callNavSetup") {
        // Set navigation as ready after a shorter delay to ensure NavHost is composed
        delay(100)
        orderScreenViewModel.isNavSetupDone(true)
    }
    val order by viewModel.collectAsState(ProductsScreenState::order)
    val isCartVisible by viewModel.collectAsState(ProductsScreenState::showCart)
    val listCarts = order.carts ?: emptyList()
    SumiNativeTheme {
        val navController = rememberNavController()
        Scaffold(modifier = Modifier
            .fillMaxSize()
            .padding(top = 55.dp),
            bottomBar = {
                MyBottomAppBar(navController = navController, orderScreenViewModel = orderScreenViewModel)
            },
            floatingActionButton = {
                if(listCarts.isNotEmpty() && !isCartVisible) {
                    FloatingActionButton(
                        backgroundColor = Color(CATEGORY_GREEN_COLOR),
                        shape = RoundedCornerShape(size = 5.dp),
                        onClick = {
                            openCart()
                        }) {
                        Text(
                            "View Cart", modifier = Modifier.padding(10.dp),
                            style = TextStyle(
                                fontFamily = fontPoppins,
                                fontWeight = FontWeight.Normal,
                                color = Color.White
                            )
                        )
                    }
                }
            }
        ) { innerPadding ->
            NavHost(navController, startDestination = StoreItems("StoreItems")) {
                composable<StoreItems> {
                    LaunchedEffect(key1 = UUID.randomUUID().toString()) {
                        if(state.selectedTables.isNotEmpty()){
                            onTableSelected()
                        }
                    }
                    tabRow(
                        viewModel = viewModel,
                        onTableRemove = onTableRemove,
                        onClickUpdateStock = { stockItem ->

                        },
                        onBackClick = onBackClick,
                        onOpenDrawer = onOpenProductDetails,
                        addDirectlyToCart = addDirectlyToCart,
                        onAddNewTableClick = onAddNewTableClick ,
                        orderScreenViewModel = orderScreenViewModel
                    )
                }
                composable<StockManagement> {
                    StockScreen(
                        viewModel = stockScreenViewModel,
                        isBackVisible = false,
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(bottom = 20.sdp)
                    ) {

                    }
                }
                composable<Reservations> {
                    ReservationsScreen(
                        viewModel = reservationsViewModel ,
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(bottom = 20.sdp)
                    )
                }
                composable<Tables> {
                    val currentRouteId by orderScreenViewModel.collectAsState(OrderState::currentRouteId)
                    val reservationsState by reservationsViewModel.collectAsState()

                    // Load reservation areas and tables when Tables tab is selected (route "1")
                    LaunchedEffect(key1 = currentRouteId) {
                        if (currentRouteId == "1") {
                            withContext(Dispatchers.IO) {
                                // Always load areas when Tables tab is selected
                                reservationsViewModel.loadReservationAreas()
                                // If there's a selected area, also load its tables
                                reservationsState.selectedAreaId?.let { areaId ->
                                    reservationsViewModel.loadReservationTables(areaId)
                                }
                            }
                        }
                    }

                    AreaTableSelectionScreen(
                        excludedTableIds = mutableListOf(),
                        onAreaTableSelected = {area, table ->
                            // Create AreaTableSelection from the selected area and table
                            val selection = AreaTableSelectionHelper.AreaTableSelection(
                                areaId = area.id,
                                areaName = area.description,
                                tableId = table.id,
                                tableName = table.tableName,
                                tableCapacity = table.seatingCapacity,
                                isOccupied = table.occupied,
                                table = table
                            )

                            // Check if there are items in cart with no table assigned
                            if (state.selectedTables.isEmpty() && viewModel.hasGlobalCartItems(state = state)) {
                                // Show AssignTableDialog with options
                                pendingTableSelection = selection
                                showCartClearDialog = true
                            } else {
                                // Handle table selection based on occupied status
                                viewModel.handleTableSelectionWithOccupiedStatus(selection, table, state = state, shouldCallSaveTable = true)
                                orderScreenViewModel.updateCurrentRoute("0")
                            }
                        },
                        onBackPressed = {

                        },
                        viewModel = reservationsViewModel,
                        productsViewModel = viewModel
                    )
                }
                composable<RegularOrders> {
                    RegularOrdersScreen(innerPadding, orderScreenViewModel, mainActivity)
                }
            }
        }

    }
}

@OptIn(ExperimentalMaterial3Api::class)
@ExperimentalFoundationApi
@Composable
fun tabRow(
    viewModel: ProductsScreenViewModel,
    onClickUpdateStock: (StockItem) -> Unit,
    onBackClick: () -> Unit,
    onOpenDrawer: (StockItem) -> Unit,
    addDirectlyToCart: (StockItem) -> Unit,
    onAddNewTableClick: () -> Unit,
    onTableRemove: (Int) -> Unit,
    orderScreenViewModel: OrderScreenViewModel
) {

    val productState by viewModel.collectAsState()
    val context = LocalContext.current
    val selectedTables by viewModel.collectAsState(ProductsScreenState::selectedTables)
    val selectedTableIndex by viewModel.collectAsState(ProductsScreenState::selectedTableIndex)
    val showTableUnselectedAlert by viewModel.collectAsState(ProductsScreenState::showTableUnselectedAlert)

    // State for confirmation dialog
    var showRemoveDialog by remember { mutableStateOf(false) }
    var tableToRemove by remember { mutableStateOf<AreaTableSelectionHelper.AreaTableSelection?>(null) }

    val response by viewModel.collectAsState(ProductsScreenState::stockItemsResponse)
    if (response is Loading || response is Uninitialized) {
        Column(
            modifier = Modifier.fillMaxSize(),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            CircularProgressIndicator(color = Color.Blue)
        }
    } else {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .fillMaxHeight()
                .padding(bottom = 20.sdp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            TopAppBar(modifier = Modifier.fillMaxWidth(),title = {
                // Display selected table information
                if (selectedTables.isNotEmpty() && selectedTableIndex < selectedTables.size && selectedTableIndex > 0) {
                    val selectedTable = selectedTables[selectedTableIndex]
                    Text(
                        text = "Selected Table: ${selectedTable.areaName} - ${selectedTable.tableName} (${selectedTable.tableCapacity} seats)",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFF2E7D32),
                        modifier = Modifier.padding(horizontal = 16.dp)
                    )
                }
            })

            // Table Selection Tabs
            TableSelectionTabs(
                selectedTables = selectedTables,
                selectedTableIndex = selectedTableIndex,
                onTableSelected = { index ->
                    viewModel.setSelectedTableIndex(index)
                    // Navigate to Store Items tab when table is selected
                    orderScreenViewModel.updateCurrentRoute("0")
                },
                onAddTableClick = {
                    onAddNewTableClick()
                },
                onRemoveTable = { tableId ->
                    // Find the table to remove and show confirmation dialog
                    val table = selectedTables.find { it.tableId == tableId }
                    if (table != null) {
                        tableToRemove = table
                        showRemoveDialog = true
                    }
                }
            )

            val tabItem = response()?.mapCategories?.keys?.map {
                TabItem(title = it ?: "")
            } ?: emptyList()

            var selectedTabIndex by remember {
                mutableIntStateOf(0)
            }

            val pagerState = rememberPagerState {
                tabItem.size
            }

            if (tabItem.size > 0) {

                LaunchedEffect(key1 = selectedTabIndex) {
                    pagerState.animateScrollToPage(selectedTabIndex)
                }

                LaunchedEffect(key1 = pagerState.currentPage, pagerState.isScrollInProgress) {
                    if (!pagerState.isScrollInProgress) selectedTabIndex = pagerState.currentPage
                }

                /*TabRow(
                    selectedTabPosition = selectedTabIndex
                ) {
                    tabItem.forEachIndexed { index, tabItem ->
                        TabTitle(tabItem.title, position = index,
                            isSelected = selectedTabIndex == index, onClick = {
                                selectedTabIndex = index
                            })
                    }
                }*/

                // Category Navigation with Overlaid Arrow Buttons
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp),
                ) {
                    // Full-width Tabs starting from edge
                    ScrollableTabRow(
                        selectedTabIndex = selectedTabIndex,
                        containerColor = Color.Transparent,
                        modifier = Modifier.fillMaxWidth(),
                        indicator = { tabPositions ->
                            // No indicator needed since we're using custom tab backgrounds
                        },
                    ) {
                        tabItem.forEachIndexed { index, tabItem ->
                            val isSelected = index == selectedTabIndex
                            val backgroundColor = if (isSelected) Color(0xFF2E7D32) else Color.Transparent

                            androidx.compose.material3.Tab(
                                selected = isSelected,
                                onClick = {
                                    selectedTabIndex = index
                                },
                                modifier = Modifier
                                    .padding(horizontal = 4.dp, vertical = 8.dp)
                                    .background(
                                        color = backgroundColor,
                                        shape = RoundedCornerShape(8.dp)
                                    )
                                    .then(
                                        if (!isSelected) {
                                            Modifier.border(
                                                width = 2.dp,
                                                color = Color(0xFF2E7D32),
                                                shape = RoundedCornerShape(8.dp)
                                            )
                                        } else Modifier
                                    )
                                    .clip(RoundedCornerShape(8.dp)),
                                text = {
                                    Text(
                                        text = tabItem.title,
                                        color = if (isSelected) Color.White else Color(0xFF2E7D32),
                                        fontFamily = fontPoppins,
                                        fontWeight = FontWeight.Bold,
                                        modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
                                    )
                                }
                            )
                        }
                    }

                    // Left Arrow Button (Overlaid)
                    IconButton(
                        onClick = {
                            if (selectedTabIndex > 0) {
                                selectedTabIndex--
                            }
                        },
                        enabled = selectedTabIndex > 0,
                        modifier = Modifier
                            .align(Alignment.CenterStart)
                            .background(
                                Color(0xFF2E7D32),
                                shape = CircleShape
                            )
                    ) {
                        Icon(
                            imageVector = Icons.Filled.KeyboardArrowLeft,
                            contentDescription = "Previous Category",
                            tint = Color.White
                        )
                    }

                    // Right Arrow Button (Overlaid)
                    IconButton(
                        onClick = {
                            if (selectedTabIndex < tabItem.size - 1) {
                                selectedTabIndex++
                            }
                        },
                        enabled = selectedTabIndex < tabItem.size - 1,
                        modifier = Modifier
                            .align(Alignment.CenterEnd)
                            .background(
                                Color(0xFF2E7D32),
                                shape = CircleShape
                            )
                    ) {
                        Icon(
                            imageVector = Icons.Filled.KeyboardArrowRight,
                            contentDescription = "Next Category",
                            tint = Color.White
                        )
                    }
                }

                HorizontalPager(
                    state = pagerState, modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f)
                ) { index ->
                    Box(modifier = Modifier.fillMaxSize()) {
                        LazyVerticalGrid(
                            columns = GridCells.Adaptive(100.sdp),
                            horizontalArrangement = Arrangement.spacedBy(10.dp),
                            verticalArrangement = Arrangement.spacedBy(10.dp),
                            contentPadding = PaddingValues(
                                bottom = 30.dp, top = 10.dp, start = 16.dp, end = 16.dp
                            )
                        ) {
                            items(
                                response()?.mapCategories?.get(tabItem[index].title) ?: emptyList()
                            ) { item ->
                                // product item with image
                                ProductItem(
                                    stockItem = item,
                                    openDrawer = onOpenDrawer,
                                    viewModel = viewModel,
                                    addDirectlyToCart = addDirectlyToCart
                                )
                            }
                        }
                    }
                }
            }
        }
    }

    // Confirmation dialog for table removal
    if (showRemoveDialog && tableToRemove != null) {
        androidx.compose.material3.AlertDialog(
            onDismissRequest = {
                showRemoveDialog = false
                tableToRemove = null
            },
            title = {
                Text(
                    text = "Remove Table",
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2E7D32)
                )
            },
            text = {
                Text(
                    text = "Do you want to remove this table?\n\n${tableToRemove!!.areaName} - ${tableToRemove!!.tableName}",
                    fontSize = 16.sp
                )
            },
            confirmButton = {
                androidx.compose.material3.TextButton(
                    onClick = {
                        tableToRemove?.let { table ->
                            onTableRemove(table.tableId)
                        }
                        showRemoveDialog = false
                        tableToRemove = null
                    }
                ) {
                    Text(
                        text = "Yes",
                        color = Color(0xFF2E7D32),
                        fontWeight = FontWeight.Bold
                    )
                }
            },
            dismissButton = {
                androidx.compose.material3.TextButton(
                    onClick = {
                        showRemoveDialog = false
                        tableToRemove = null
                    }
                ) {
                    Text(
                        text = "No",
                        color = Color.Gray,
                        fontWeight = FontWeight.Bold
                    )
                }
            },
            containerColor = Color.White,
            titleContentColor = Color(0xFF2E7D32),
            textContentColor = Color.Black
        )
    }

    // Table Unselected Alert Dialog
    if (showTableUnselectedAlert) {
        androidx.compose.material3.AlertDialog(
            onDismissRequest = {
                viewModel.hideTableUnselectedAlert()
            },
            title = {
                Text(
                    text = "Table Unselected",
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2E7D32)
                )
            },
            text = {
                androidx.compose.material.Text(
                    text = "Select a table before adding items.",
                    fontSize = 16.sp
                )
            },
            confirmButton = {
                androidx.compose.material3.TextButton(
                    onClick = {
                        viewModel.hideTableUnselectedAlert()
                    }
                ) {
                    Text(
                        text = "OK",
                        color = Color(0xFF2E7D32),
                        fontWeight = FontWeight.Bold
                    )
                }
            },
            containerColor = Color.White,
            titleContentColor = Color(0xFF2E7D32),
            textContentColor = Color.Black
        )
    }
}

@Composable
fun ProductItem(
    stockItem: StockItem,
    openDrawer: (StockItem) -> Unit,
    viewModel: ProductsScreenViewModel,
    addDirectlyToCart: (StockItem) -> Unit
) {
    val state by viewModel.collectAsState()
    var isLoading by remember { mutableStateOf(false) }
    val coroutineScope = rememberCoroutineScope()

    Card(modifier = Modifier
        .padding(4.dp)
        .fillMaxWidth()
        .clickable {
            if (!isLoading && stockItem.stock == 1) {
                isLoading = true
                // Call getOptionDetails to check if item has options
                coroutineScope.launch {
                    viewModel.getOptionDetails(stockItem.id ?: 0, stockItem = stockItem, state = state)
                }
            }
        }) {
        Box {
            Column(
                modifier = Modifier.padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {

                val imageUrl = "$BASE_DOMAIN/dasa/streamer?name=${stockItem.pic}"

                val request: ImageRequest =
                    ImageRequest.Builder(LocalContext.current.applicationContext).data(imageUrl)
                        .crossfade(true).diskCacheKey(imageUrl).diskCachePolicy(CachePolicy.ENABLED)
                        .setHeader("Cache-Control", "max-age=31536000").build()

                // Image
                AsyncImage(
                    model = request,
                    contentDescription = stockItem.name,
                    modifier = Modifier
                        .height(84.sdp)
                        .fillMaxWidth()
                        .clip(RoundedCornerShape(8.dp)),
                    contentScale = ContentScale.Crop
                )

                Spacer(modifier = Modifier.height(12.dp))

                // Product Name
                Text(
                    text = (stockItem.name ?: "").uppercase(),
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    textAlign = TextAlign.Center,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier.fillMaxWidth()
                )

                Spacer(modifier = Modifier.height(8.dp))

                // Price
                Text(
                    text = "£${stockItem.price?.transformDecimal() ?: "0.00"}",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    textAlign = TextAlign.Center
                )

                Spacer(modifier = Modifier.height(8.dp))

                // Stock Status
                if (stockItem.stock != 1) {
                    Text(
                        text = getStockString(stockItem.stock ?: 0).uppercase(),
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Bold,
                        color = Color.Red,
                        textAlign = TextAlign.Center
                    )
                }
            }

            // Loading overlay
            if (isLoading) {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(Color.Black.copy(alpha = 0.3f))
                        .clip(RoundedCornerShape(8.dp)),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator(
                        color = Color.Blue,
                        modifier = Modifier.size(24.dp)
                    )
                }
            }
        }
    }

    // Listen for option details response
    val optionDetailsResponse by viewModel.collectAsState(ProductsScreenState::optionDetailsResponse)

    LaunchedEffect(optionDetailsResponse) {
        if (isLoading && optionDetailsResponse is Success) {
            val optionDetails = optionDetailsResponse()
            if (optionDetails != null) {
                val hasOptions = optionDetails.optionSets?.isNotEmpty() == true &&
                        optionDetails.optionSets.any { it.options.isNotEmpty() }

                if (hasOptions) {
                    // Has options - open details screen
                    openDrawer(stockItem)
                } else {
                    // No options - add directly to cart
                    addDirectlyToCart(stockItem)
                }
                isLoading = false
            }
        } else if (isLoading && optionDetailsResponse !is Loading) {
            // Error or other state - stop loading
            isLoading = false
        }
    }
}


fun getStockString(stock: Int): String {
    return when (stock) {
        1 -> "In Stock"
        2 -> "Sold out for Today"
        else -> "Off The Menu"
    }
}

// Data Class to handle items
data class TabItem(
    val title: String
)

@Composable
private fun MyTabIndicator(
    indicatorWidth: Dp,
    indicatorOffset: Dp,
    indicatorColor: Color,
) {
    Box(
        modifier = Modifier
            .fillMaxHeight()
            .width(
                width = indicatorWidth,
            )
            .offset(
                x = indicatorOffset,
            )
            .clip(
                shape = CircleShape,
            )
            .background(
                color = indicatorColor,
            ),
    )
}

@Composable
private fun MyTabItem(
    isSelected: Boolean,
    onClick: () -> Unit,
    tabWidth: Dp,
    text: String,
) {
    val tabTextColor: Color by animateColorAsState(
        targetValue = if (isSelected) {
            White
        } else {
            Black
        },
        animationSpec = tween(easing = LinearEasing),
    )
    Text(
        modifier = Modifier
            .clip(CircleShape)
            .clickable {
                onClick()
            }
            .width(tabWidth)
            .padding(
                vertical = 8.dp,
                horizontal = 12.dp,
            ),
        text = text,
        color = tabTextColor,
        textAlign = TextAlign.Center,
    )
}

@Composable
fun MyBottomAppBar(navController: NavController, orderScreenViewModel : OrderScreenViewModel) {
    val currentRouteId by orderScreenViewModel.collectAsState(OrderState::currentRouteId)
    val isNavSetupDone by orderScreenViewModel.collectAsState(OrderState::isNavSetupDone)
    val routes = if(orderScreenViewModel.prefs.storeConfigurations?.data?.reservationsEnabled == true){
        topLevelRoutesReservations
    }
    else {
        topLevelRoutes
    }
    if (isNavSetupDone) {
        when (currentRouteId) {
            "0" -> {
                navigate(navController, routes[0])
            }
            "1" -> {
                navigate(navController, routes[1])
            }
            "2" -> {
                navigate(navController, routes[2])
            }
            "3" -> {
                if (routes.size > 3) navigate(navController, routes[3])
            }
            else -> {
                navigate(navController, routes.last())
            }
        }
    }
    BottomNavigation(
        elevation = 0.dp, contentColor = Color.White, backgroundColor = Color(CATEGORY_GREEN_COLOR).copy(0.6f)
    ) {
        val navBackStackEntry by navController.currentBackStackEntryAsState()
        val currentDestination = navBackStackEntry?.destination
        routes.forEachIndexed { index, topLevelRoute ->
            val isSelected = index.toString() == currentRouteId
            BottomNavigationItem(
                modifier = if (isSelected) {
                    Modifier
                        .clip(RoundedCornerShape(25.dp))
                        .background(color = Color(CATEGORY_GREEN_COLOR))
                        .clipToBounds()
                } else {
                    Modifier.background(color = Color.Transparent)
                },
                icon = { androidx.compose.material.Icon(topLevelRoute.icon, contentDescription = topLevelRoute.name) },
                label = {
                    Text(
                        topLevelRoute.name, color = if (isSelected) Color.White else Color.Black
                    )
                },
                selected = isSelected,
                onClick = {
                    orderScreenViewModel.updateCurrentRoute(index.toString())
                    navigate(navController, topLevelRoute)
                },
                selectedContentColor = Color.White,
                unselectedContentColor = Color.White,
                alwaysShowLabel = true,
            )
        }
    }
}

fun navigate(navController: NavController, topLevelRoute: TopLevelRoute<out Any>) {
    try {
        navController.navigate(topLevelRoute.route) {
            // Pop up to the start destination of the graph to
            // avoid building up a large stack of destinations
            // on the back stack as users select items
            try {
                popUpTo(navController.graph.findStartDestination().id) {
                    saveState = true
                }
            } catch (e: IllegalStateException) {
                // Navigation graph not set up yet, skip popUpTo
            }
            // Avoid multiple copies of the same destination when
            // reselecting the same item
            launchSingleTop = true
            // Restore state when reselecting a previously selected item
            restoreState = true
        }
    } catch (e: IllegalStateException) {
        // Navigation graph not ready yet, ignore navigation attempt
    }
}

@Composable
fun RegularOrdersScreen(innerPadding: PaddingValues, orderScreenViewModel: OrderScreenViewModel,
                        mainActivity: MainActivity
) {
    LaunchedEffect(key1 = "callOrders") {
        withContext(Dispatchers.IO){
            orderScreenViewModel.getOrders(isShowAllOrders = false)
            orderScreenViewModel.getScheduleOrders()
        }
    }
    OrderScreenTopFunction(modifier = Modifier.padding(innerPadding),
        viewModel = orderScreenViewModel,
        onPrintBill = { bitmap ->
            printOrderBitmap(bitmap, mainActivity)
        },
        onTrackingUrlClick = { url ->

            val finalUrl = if (!url.startsWith("http://") && !url.startsWith("https://")) {
                "http://$url"
            } else url

            val browserIntent = Intent(Intent.ACTION_VIEW, Uri.parse(finalUrl))
            val packageManager: PackageManager = mainActivity.packageManager
            if (browserIntent.resolveActivity(packageManager) != null) {
                mainActivity.startActivity(browserIntent)
            } else {
                MaterialDialog(mainActivity).show {
                    title(text = "Error")
                    message(text = "No browser found to open the tracking url")
                    positiveButton(text = "Ok") {
                        it.dismiss()
                    }
                }
            }

        },
        onUpdateShowAllOrders = {
            orderScreenViewModel.updateShowAllOrders(it)
            mainActivity.lifecycleScope.launch(Dispatchers.IO) {
                orderScreenViewModel.getOrders(isShowAllOrders = it)
                orderScreenViewModel.getScheduleOrders()
            }
        },
        callOrders = {
            mainActivity.showDialogWhilePlaying()
        })
}

@Composable
fun TableSelectionTabs(
    selectedTables: List<AreaTableSelectionHelper.AreaTableSelection>,
    selectedTableIndex: Int,
    onTableSelected: (Int) -> Unit,
    onAddTableClick: () -> Unit,
    onRemoveTable: (Int) -> Unit
) {
    if (selectedTables.isNotEmpty() || true) { // Always show to allow adding tables
        ScrollableTabRow (
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 12.dp),
            selectedTabIndex = selectedTableIndex,
            containerColor = Color.Transparent,
            edgePadding = 0.dp,
            indicator = {
                // No indicator needed since we're using custom tab backgrounds
            }
        ) {
            // Table tabs with improved spacing
            selectedTables.forEachIndexed { index, table ->
                Box(
                    modifier = Modifier.padding(end = if (index < selectedTables.size - 1) 12.dp else 8.dp)
                ) {
                    TableTab(
                        table = table,
                        isSelected = index == selectedTableIndex,
                        onClick = { onTableSelected(index) },
                        onRemove = { onRemoveTable(table.tableId) }
                    )
                }
            }

            // Add button with improved spacing
            Box(
                modifier = Modifier.padding(start = 8.dp)
            ) {
                AddTableButton(onClick = onAddTableClick)
            }
        }
    }
}

@Composable
fun TableTab(
    table: AreaTableSelectionHelper.AreaTableSelection,
    isSelected: Boolean,
    onClick: () -> Unit,
    onRemove: () -> Unit
) {
    val backgroundColor = if (isSelected) Color(0xFF2E7D32) else Color.White
    val textColor = if (isSelected) Color.White else Color(0xFF2E7D32)
    val borderColor = Color(0xFF2E7D32)

    Card(
        modifier = Modifier
            .width(200.dp)
            .clickable { onClick() }
            .border(
                width = 2.dp,
                color = borderColor,
                shape = RoundedCornerShape(12.dp)
            ),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(containerColor = backgroundColor),
        elevation = CardDefaults.cardElevation(defaultElevation = if (isSelected) 6.dp else 3.dp)
    ) {
        Row(
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 12.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = table.areaName,
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Medium,
                    color = textColor.copy(alpha = 0.8f),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                Text(
                    text = table.tableName,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                    color = textColor,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
            }

            // Close button with improved styling
            IconButton(
                onClick = onRemove,
                modifier = Modifier
                    .size(28.dp)
                    .background(
                        color = textColor.copy(alpha = 0.1f),
                        shape = CircleShape
                    )
            ) {
                Icon(
                    imageVector = Icons.Default.Close,
                    contentDescription = "Remove table",
                    tint = textColor,
                    modifier = Modifier.size(18.dp)
                )
            }
        }
    }
}

@Composable
fun AddTableButton(onClick: () -> Unit) {
    Card(
        modifier = Modifier
            .clickable { onClick() }
            .border(
                width = 2.dp,
                color = Color(0xFF2E7D32),
                shape = RoundedCornerShape(12.dp)
            ),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 3.dp)
    ) {
        Row(
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 12.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Box(
                modifier = Modifier
                    .size(32.dp)
                    .background(
                        color = Color(0xFF2E7D32).copy(alpha = 0.1f),
                        shape = CircleShape
                    ),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = Icons.Outlined.Add,
                    contentDescription = "Add table",
                    tint = Color(0xFF2E7D32),
                    modifier = Modifier.size(20.dp)
                )
            }
            Column {
                Text(
                    text = "Add Table",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2E7D32)
                )
                Text(
                    text = "Select from tables tab",
                    fontSize = 12.sp,
                    color = Color(0xFF2E7D32).copy(alpha = 0.7f)
                )
            }
        }
    }
}

@Composable
fun OpenTablesScreen(
    reservationsViewModel: ReservationsViewModel,
    productsViewModel: ProductsScreenViewModel,
    onTableSelected: (com.thedasagroup.suminative.data.model.response.reservations.Area, com.thedasagroup.suminative.data.model.response.reservations.Table) -> Unit
) {
    val reservationsState by reservationsViewModel.collectAsState()
    val productsState by productsViewModel.collectAsState()
    val selectedTables by productsViewModel.collectAsState(ProductsScreenState::selectedTables)

    // Load tables for all areas when areas are loaded
    LaunchedEffect(reservationsState.areasResponse) {
        if (reservationsState.areasResponse is com.airbnb.mvrx.Success) {
            reservationsViewModel.loadTablesForAllAreas()
        }
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White)
            .padding(16.dp)
    ) {
        // Header
        Text(
            text = "Available Tables",
            fontSize = 24.sp,
            fontWeight = FontWeight.Bold,
            color = Color(0xFF2E7D32),
            modifier = Modifier.padding(bottom = 16.dp)
        )

        // Selected Tables Section (if any)
        if (selectedTables.isNotEmpty()) {
            Text(
                text = "Selected Tables",
                fontSize = 18.sp,
                fontWeight = FontWeight.Medium,
                color = Color.Black,
                modifier = Modifier.padding(bottom = 8.dp)
            )

            // Selected Tables with improved padding
            LazyVerticalGrid(
                columns = GridCells.Adaptive(200.dp),
                horizontalArrangement = Arrangement.spacedBy(12.dp),
                verticalArrangement = Arrangement.spacedBy(12.dp),
                contentPadding = PaddingValues(bottom = 16.dp),
                modifier = Modifier.height(120.dp)
            ) {
                items(selectedTables) { selectedTable ->
                    SelectedTableCard(
                        table = selectedTable,
                        cartTotal = productsState.tableOrders[selectedTable.tableId]?.net() ?: 0.0,
                        onRemove = {  }
                    )
                }
            }

            Spacer(modifier = Modifier.height(16.dp))
        }

        // Available Tables Section
        Text(
            text = "All Available Tables",
            fontSize = 18.sp,
            fontWeight = FontWeight.Medium,
            color = Color.Black,
            modifier = Modifier.padding(bottom = 8.dp)
        )

        // Tables content
        when (val areasResponse = reservationsState.areasResponse) {
            is com.airbnb.mvrx.Loading -> {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator(color = Color(0xFF2E7D32))
                }
            }
            is com.airbnb.mvrx.Success -> {
                val areas = areasResponse.invoke()
                if (areas.isEmpty()) {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = "No dining areas available",
                            fontSize = 16.sp,
                            color = Color.Gray
                        )
                    }
                } else {
                    LazyVerticalGrid(
                        columns = GridCells.Adaptive(180.dp),
                        horizontalArrangement = Arrangement.spacedBy(16.dp),
                        verticalArrangement = Arrangement.spacedBy(16.dp),
                        contentPadding = PaddingValues(bottom = 16.dp)
                    ) {
                        areas.forEach { area ->
                            val tablesResponse = reservationsState.areaTablesMap[area.id]
                            when (tablesResponse) {
                                is com.airbnb.mvrx.Success -> {
                                    val tables = tablesResponse.invoke()
                                    val availableTables = tables.filter { table ->
                                        !table.occupied && !table.reserved &&
                                                !selectedTables.any { it.tableId == table.id }
                                    }

                                    items(availableTables) { table ->
                                        OpenTableCard(
                                            area = area,
                                            table = table,
                                            cartTotal = productsState.tableOrders[table.id]?.totalPrice ?: 0.0,
                                            onClick = { onTableSelected(area, table) }
                                        )
                                    }
                                }
                                else -> {
                                    // Loading or error state for this area
                                }
                            }
                        }
                    }
                }
            }
            is com.airbnb.mvrx.Fail -> {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = "Failed to load tables",
                            fontSize = 16.sp,
                            color = Color.Red
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "Please try again",
                            fontSize = 14.sp,
                            color = Color.Gray
                        )
                    }
                }
            }
            else -> {
                // Uninitialized state
            }
        }
    }
}

@Composable
fun SelectedTableCard(
    table: AreaTableSelectionHelper.AreaTableSelection,
    cartTotal: Double,
    onRemove: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .border(
                width = 2.dp,
                color = Color(0xFF2E7D32),
                shape = RoundedCornerShape(12.dp)
            ),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(containerColor = Color(0xFF2E7D32).copy(alpha = 0.1f)),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Top
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = "${table.areaName}",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFF2E7D32),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                    Text(
                        text = table.tableName,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.Black,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }

                IconButton(
                    onClick = onRemove,
                    modifier = Modifier.size(24.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Close,
                        contentDescription = "Remove table",
                        tint = Color(0xFF2E7D32),
                        modifier = Modifier.size(18.dp)
                    )
                }
            }

            if (cartTotal > 0.0) {
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "£${cartTotal.transformDecimal()}",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2E7D32)
                )
            }
        }
    }
}

@Composable
fun OpenTableCard(
    area: com.thedasagroup.suminative.data.model.response.reservations.Area,
    table: com.thedasagroup.suminative.data.model.response.reservations.Table,
    cartTotal: Double,
    onClick: () -> Unit
) {
    val tableDetails = table.getTableDetails()

    // Parse color from tableDetailsJson or use default
    val customColor = try {
        tableDetails?.color?.let { colorString ->
            if (colorString.startsWith("#")) {
                Color(android.graphics.Color.parseColor(colorString))
            } else {
                Color(0xFF2E7D32) // Default green
            }
        } ?: Color(0xFF2E7D32)
    } catch (e: Exception) {
        Color(0xFF2E7D32) // Default green if parsing fails
    }

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .border(
                width = 2.dp,
                color = customColor,
                shape = RoundedCornerShape(12.dp)
            ),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = area.description,
                fontSize = 12.sp,
                fontWeight = FontWeight.Medium,
                color = customColor,
                textAlign = TextAlign.Center,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )

            Spacer(modifier = Modifier.height(4.dp))

            Text(
                text = table.tableName,
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold,
                color = Color.Black,
                textAlign = TextAlign.Center,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )

            if (cartTotal > 0.0) {
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "£${cartTotal.transformDecimal()}",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Bold,
                    color = customColor,
                    textAlign = TextAlign.Center
                )
            }
        }
    }
}